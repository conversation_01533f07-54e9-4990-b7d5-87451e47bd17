// Base TodoItem interface (existing)
export interface TodoItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  assignedTo?: string;
  assignedToName?: string;
  assignedToPhotoURL?: string;
  createdBy: string;
  groupId?: string;
}

// Enhanced TodoRow interface for Supabase database operations
// Matches the actual database schema with lowercase column names
export interface EnhancedTodoRow {
  // Existing columns (unchanged)
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  assignedTo?: string;
  assignedToName?: string;
  assignedToPhotoUrl?: string; // Note: database uses assignedToPhotoUrl, not assignedToPhotoURL
  createdBy: string;
  groupId?: string;
  columnId?: string; // Keeping this for now, but will prefer column_id
  column_id?: string; // Standardizing to column_id for database

  // New enhanced columns (lowercase as stored in database)
  parentid?: string;
  subjectid?: string;
  examid?: PresetExamType; // Fixed: database uses examid, not exam_id
  tags: string[];
  chaptertags: string[];
  difficultylevel: 'easy' | 'medium' | 'hard';
  timeestimate?: number; // in minutes
  actualtimespent?: number; // in minutes
  completionpercentage: number; // 0-100
  notes?: string;
  viewcount: number;
  lastviewed?: number;
}

// Enhanced TodoItem interface for client-side use
// Uses camelCase for better TypeScript experience
export interface EnhancedTodoItem extends TodoItem {
  // New enhanced fields (camelCase for client use)
  parentId?: string;
  subjectId?: string;
  examId?: PresetExamType; // Changed from examType to examId
  columnId?: string; // Added columnId
  tags: string[];
  chapterTags: string[];
  difficultyLevel: 'easy' | 'medium' | 'hard';
  timeEstimate?: number; // in minutes
  actualTimeSpent?: number; // in minutes
  completionPercentage: number; // 0-100
  notes?: string;
  viewCount: number;
  lastViewed?: number;

  // Computed fields (calculated on client, not stored in database)
  depth?: number; // Calculated from parentId chain
  subtasks?: string[]; // Calculated by querying children
  subjectColor?: string; // Fetched from userSubjects
  subjectName?: string; // Fetched from userSubjects
  examName?: string; // Fetched from exams
  examDate?: number; // Fetched from exams
  isOverdue?: boolean; // Calculated from dueDate
  hasSubtasks?: boolean; // Calculated from children count
}

// Subject interface for integration
export interface Subject {
  id: string;
  name: string;
  color: string;
  userId: string;
  createdAt?: string;

  // Computed statistics (calculated on client)
  totalTasks?: number;
  completedTasks?: number;
  overdueTasks?: number;
  completionPercentage?: number;
  averagePriority?: number;
}

// Preset exam types for exam preparation features
export type PresetExamType =
  | 'JEE_MAIN'
  | 'JEE_ADVANCED'
  | 'NEET'
  | 'BITSAT'
  | 'WBJEE'
  | 'MHT_CET'
  | 'KCET'
  | 'COMEDK'
  | 'VITEEE'
  | 'SRMJEEE'
  | 'CBSE_12'
  | 'ICSE_12'
  | 'STATE_BOARD'
  | 'SAT'
  | 'OTHER';

export interface PresetExam {
  id: PresetExamType;
  name: string;
  fullName: string;
  description: string;
  subjects: string[]; // Common subject names
  duration: number; // in minutes
  totalMarks: number;
  category: 'Engineering' | 'Medical' | 'Board' | 'International' | 'Other';
  difficulty: 'easy' | 'medium' | 'hard';
}

// Legacy Exam interface for integration (keeping for compatibility)
export interface Exam {
  id: string;
  userId: string;
  name: string;
  date: string; // ISO date string
  totalMarks: number;
  totalMarksObtained: number;
  subjectMarks?: any; // JSONB field
  notes?: string;
  createdAt?: string;
}

// Filter state interface
export interface FilterState {
  subjects: string[];
  priorities: ('low' | 'medium' | 'high')[];
  statuses: string[];
  dateRange: {
    start?: Date;
    end?: Date;
  };
  tags: string[];
  examIds: string[];
  showOverdue: boolean;
  showCompleted: boolean;
  difficultyLevels: ('easy' | 'medium' | 'hard')[];
}

// Search state interface
export interface SearchState {
  query: string;
  filters: FilterState;
  savedFilters: SavedFilter[];
  activeFilterId?: string;
}

// Saved filter interface
export interface SavedFilter {
  id: string;
  name: string;
  filters: FilterState;
  createdAt: number;
}

// Analytics data interface
export interface AnalyticsData {
  completionRates: {
    overall: number;
    bySubject: Record<string, number>;
    byPriority: Record<string, number>;
    byDifficulty: Record<string, number>;
  };
  taskVelocity: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  studyPatterns: {
    productiveHours: number[];
    averageTaskDuration: number;
    completionStreak: number;
  };
  subjectDistribution: Record<string, number>;
  upcomingDeadlines: EnhancedTodoItem[];
}

// Loading state interface
export interface LoadingState {
  tasks: boolean;
  subjects: boolean;
  exams: boolean;
  analytics: boolean;
}

// Error state interface
export interface ErrorState {
  tasks?: string;
  subjects?: string;
  exams?: string;
  analytics?: string;
}

// Enhanced TodoColumn interface
export interface TodoColumn {
  id: string;
  title: string;
  taskIds: string[];
  color?: string;
  limit?: number;
}

// Enhanced TodoBoard interface
export interface EnhancedTodoBoard {
  tasks: Record<string, EnhancedTodoItem>;
  columns: Record<string, TodoColumn>;
  columnOrder: string[];
}

// Enhanced TodoState interface
export interface EnhancedTodoState {
  // Core data
  board: EnhancedTodoBoard;
  subjects: Record<string, Subject>;
  presetExams: Record<PresetExamType, PresetExam>;

  // UI state
  search: SearchState;
  selectedTasks: string[];
  viewMode: 'kanban' | 'table' | 'calendar';

  // Analytics
  analytics: AnalyticsData;

  // Loading and error states
  loading: LoadingState;
  error: ErrorState;
}

// Legacy interfaces (for backward compatibility)
export interface TodoBoard {
  tasks: Record<string, TodoItem>;
  columns: Record<string, TodoColumn>;
  columnOrder: string[];
}

export interface TodoState {
  board: TodoBoard;
  loading: boolean;
  error: string | null;
}

// Utility types for data transformation
export type TodoRowToItem = (row: EnhancedTodoRow) => EnhancedTodoItem;
export type TodoItemToRow = (item: Partial<EnhancedTodoItem>) => Partial<EnhancedTodoRow>;

// Drag and drop interfaces
export interface EnhancedDragItem {
  droppableId: string;
  index: number;
  taskId?: string;
}

// Bulk operation interfaces
export interface BulkOperation {
  type: 'complete' | 'delete' | 'priority' | 'subject' | 'status' | 'exam' | 'assign'; // Added 'exam' and 'assign'
  taskIds: string[];
  value?: any;
}

// Task template interfaces
export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  tasks: Partial<EnhancedTodoItem>[];
  category: 'exam' | 'subject' | 'custom';
  createdAt: number;
  createdBy: string;
}
