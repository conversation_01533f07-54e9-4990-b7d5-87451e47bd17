import { supabase } from './supabaseClient';
import { EnhancedTodoRow, EnhancedTodoItem, Subject, Exam } from '../types/todo';
import { 
  normalizeEnhancedTodoRow, 
  todoRowToItem, 
  todoItemToRow,
  validateEnhancedTodo 
} from './todoCompatibility';

/**
 * Enhanced Supabase utilities for advanced task management
 * Handles new enhanced columns while maintaining backward compatibility
 */

// Ensure user is authenticated
const ensureAuthenticated = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error || !session) {
    throw new Error('User not authenticated');
  }
  return session;
};

/**
 * Enhanced getTodos function that handles all new columns
 */
export const getEnhancedTodos = async (userId: string): Promise<EnhancedTodoItem[]> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Fetching enhanced todos for user:', userId);

    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId) // Fixed: use camelCase field name
      .order('createdAt', { ascending: false }); // Fixed: use camelCase field name

    if (error) {
      console.error('Supabase error getting enhanced todos:', error);
      throw error;
    }

    console.log('Fetched enhanced todos:', data?.length || 0, 'items');
    
    // Convert database rows to enhanced todo items
    const enhancedTodos = (data || []).map((row: any) => {
      try {
        const normalizedRow = normalizeEnhancedTodoRow(row);
        return todoRowToItem(normalizedRow);
      } catch (itemError) {
        console.warn('Error processing todo item:', row.id, itemError);
        // Return a basic todo item if processing fails
        return {
          id: row.id || '',
          title: row.title || 'Untitled Task',
          description: row.description || '',
          priority: row.priority || 'medium',
          createdAt: row.createdAt || row.created_at || Date.now(),
          updatedAt: row.updatedAt || row.updated_at || Date.now(),
          createdBy: row.createdBy || row.created_by || userId,
          dueDate: row.dueDate || row.due_date,
          assignedTo: row.assignedTo || row.assigned_to,
          assignedToName: row.assignedToName || row.assigned_to_name,
          assignedToPhotoURL: row.assignedToPhotoUrl || row.assigned_to_photo_url,
          groupId: row.groupId || row.group_id,
          columnId: row.columnId || row.column_id || 'column-1',
          // Enhanced fields with defaults
          parentId: undefined,
          subjectId: undefined,
          examId: undefined,
          tags: [],
          chapterTags: [],
          difficultyLevel: 'medium' as const,
          timeEstimate: undefined,
          actualTimeSpent: undefined,
          completionPercentage: 0,
          notes: undefined,
          viewCount: 0,
          lastViewed: undefined,
          // Computed fields
          depth: 0,
          subtasks: [],
          hasSubtasks: false,
          isOverdue: false,
        } as EnhancedTodoItem;
      }
    });

    return enhancedTodos;
  } catch (error) {
    console.error('Error getting enhanced todos:', error);
    // Return empty array instead of throwing to prevent page crash
    return [];
  }
};

/**
 * Enhanced createTodo function with validation and new columns
 */
export const createEnhancedTodo = async (todoData: Partial<EnhancedTodoItem>): Promise<EnhancedTodoItem> => {
  try {
    const session = await ensureAuthenticated();

    // Ensure createdBy is set to current user if not provided
    const todoWithUser = {
      ...todoData,
      createdBy: todoData.createdBy || session.user.id,
    };

    // Validate todo data for creation
    const validationErrors = validateEnhancedTodo(todoWithUser, false);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Convert to database format
    const rowData = todoItemToRow(todoData);
    
    // Ensure required fields and generate ID if not provided
    const insertData = {
      ...rowData,
      id: todoData.id || `todo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      createdBy: todoData.createdBy || session.user.id,
      title: todoData.title || 'Untitled Task',
    };

    console.log('Creating enhanced todo:', insertData);

    const { data, error } = await supabase
      .from('todos')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating enhanced todo:', error);
      throw error;
    }

    console.log('Enhanced todo created successfully:', data);
    
    // Convert back to enhanced todo item
    const normalizedRow = normalizeEnhancedTodoRow(data);
    return todoRowToItem(normalizedRow);
  } catch (error) {
    console.error('Error creating enhanced todo:', error);
    throw error;
  }
};

/**
 * Enhanced updateTodo function with validation and new columns
 */
export const updateEnhancedTodo = async (
  todoId: string,
  updates: Partial<EnhancedTodoItem>
): Promise<EnhancedTodoItem> => {
  try {
    await ensureAuthenticated();

    // Validate updates (this is an update operation)
    const validationErrors = validateEnhancedTodo(updates, true);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Convert to database format
    const rowUpdates = todoItemToRow(updates);
    
    // Add updated timestamp
    const updateData = {
      ...rowUpdates,
      updatedAt: Date.now(),
    };

    console.log('Updating enhanced todo:', todoId, updateData);

    const { data, error } = await supabase
      .from('todos')
      .update(updateData)
      .eq('id', todoId)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating enhanced todo:', error);
      throw error;
    }

    console.log('Enhanced todo updated successfully:', data);
    
    // Convert back to enhanced todo item
    const normalizedRow = normalizeEnhancedTodoRow(data);
    return todoRowToItem(normalizedRow);
  } catch (error) {
    console.error('Error updating enhanced todo:', error);
    throw error;
  }
};

/**
 * Enhanced deleteTodo function with cascade handling for subtasks
 */
export const deleteEnhancedTodo = async (todoId: string, deleteSubtasks: boolean = false): Promise<void> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Deleting enhanced todo:', todoId, 'deleteSubtasks:', deleteSubtasks);

    if (deleteSubtasks) {
      // First, get all subtasks
      const { data: subtasks, error: subtasksError } = await supabase
        .from('todos')
        .select('id')
        .eq('parentid', todoId);

      if (subtasksError) {
        console.error('Error fetching subtasks:', subtasksError);
        throw subtasksError;
      }

      // Delete all subtasks recursively
      if (subtasks && subtasks.length > 0) {
        for (const subtask of subtasks) {
          await deleteEnhancedTodo(subtask.id, true);
        }
      }
    } else {
      // Note: Parent ID operations are skipped until the todos table is updated with enhanced columns
      console.log('Skipping parent ID update - enhanced columns not available in current todos table');
    }

    // Delete the main task
    const { error } = await supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) {
      console.error('Supabase error deleting enhanced todo:', error);
      throw error;
    }

    console.log('Enhanced todo deleted successfully:', todoId);
  } catch (error) {
    console.error('Error deleting enhanced todo:', error);
    throw error;
  }
};

/**
 * Get user subjects for task integration
 */
export const getUserSubjects = async (userId: string): Promise<Subject[]> => {
  try {
    const session = await ensureAuthenticated();
    console.log('Fetching user subjects for:', userId);

    const { data, error } = await supabase
      .from('userSubjects')
      .select('*')
      .eq('userId', userId) // Fixed: use camelCase field name
      .order('name', { ascending: true });

    if (error) {
      console.error('Supabase error getting user subjects:', error);
      throw error;
    }

    console.log('Fetched user subjects:', data?.length || 0, 'items');
    return data || [];
  } catch (error) {
    console.error('Error getting user subjects:', error);
    return [];
  }
};

// Note: getUserExams removed - now using preset exams instead of database exams

/**
 * Enhanced subscription for real-time updates
 */
export const subscribeToEnhancedTodos = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('enhanced-todos-changes')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `createdBy=eq.${userId}`
      },
      (payload) => {
        console.log('Enhanced todo real-time update:', payload);
        callback(payload);
      }
    )
    .subscribe();
};

/**
 * Bulk operations for enhanced todos
 */
export const bulkUpdateEnhancedTodos = async (
  todoIds: string[],
  updates: Partial<EnhancedTodoItem>
): Promise<EnhancedTodoItem[]> => {
  try {
    await ensureAuthenticated();

    // Validate updates (this is an update operation)
    const validationErrors = validateEnhancedTodo(updates, true);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Convert to database format
    const rowUpdates = todoItemToRow(updates);
    const updateData = {
      ...rowUpdates,
      updatedAt: Date.now(),
    };

    console.log('Bulk updating enhanced todos:', todoIds, updateData);

    const { data, error } = await supabase
      .from('todos')
      .update(updateData)
      .in('id', todoIds)
      .select();

    if (error) {
      console.error('Supabase error bulk updating enhanced todos:', error);
      throw error;
    }

    console.log('Enhanced todos bulk updated successfully:', data?.length || 0, 'items');
    
    // Convert back to enhanced todo items
    return (data || []).map((row: any) => {
      const normalizedRow = normalizeEnhancedTodoRow(row);
      return todoRowToItem(normalizedRow);
    });
  } catch (error) {
    console.error('Error bulk updating enhanced todos:', error);
    throw error;
  }
};

/**
 * Get todos with hierarchical structure
 */
export const getTodosWithHierarchy = async (userId: string): Promise<EnhancedTodoItem[]> => {
  try {
    const todos = await getEnhancedTodos(userId);
    const subjects = await getUserSubjects(userId);

    // Create lookup maps
    const subjectsMap = subjects.reduce((acc, subject) => {
      acc[subject.id] = subject;
      return acc;
    }, {} as Record<string, Subject>);

    const todosMap = todos.reduce((acc, todo) => {
      acc[todo.id] = todo;
      return acc;
    }, {} as Record<string, EnhancedTodoItem>);

    // Calculate computed fields for all todos
    return todos.map(todo => {
      // Calculate computed fields
      const computed = { ...todo };

      // Calculate depth
      computed.depth = calculateDepth(todo.id, todosMap);

      // Find subtasks
      computed.subtasks = todos
        .filter(t => t.parentId === todo.id)
        .map(t => t.id);

      computed.hasSubtasks = computed.subtasks.length > 0;

      // Add subject info
      if (todo.subjectId && subjectsMap[todo.subjectId]) {
        computed.subjectName = subjectsMap[todo.subjectId].name;
        computed.subjectColor = subjectsMap[todo.subjectId].color;
      }

      // Add exam info (using preset exams)
      if (todo.examId) {
        // Preset exams don't have specific dates, so we'll use the exam type info
        computed.examName = todo.examId;
      }

      // Check if overdue
      computed.isOverdue = !!(todo.dueDate && todo.dueDate < Date.now());

      return computed;
    });
  } catch (error) {
    console.error('Error getting todos with hierarchy:', error);
    throw error;
  }
};

/**
 * Helper function to calculate task depth
 */
const calculateDepth = (
  taskId: string,
  todosMap: Record<string, EnhancedTodoItem>,
  visited: Set<string> = new Set()
): number => {
  if (visited.has(taskId)) {
    return 0; // Prevent infinite loops
  }

  const task = todosMap[taskId];
  if (!task || !task.parentId) {
    return 0;
  }

  visited.add(taskId);
  return 1 + calculateDepth(task.parentId, todosMap, visited);
};
