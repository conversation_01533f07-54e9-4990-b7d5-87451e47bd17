import { TodoItem, EnhancedTodoItem, EnhancedTodoRow } from '../types/todo';

/**
 * Backward compatibility utilities for enhanced task management system
 * Ensures existing tasks work seamlessly with new enhanced features
 */

/**
 * Normalizes a todo row from database to ensure all new columns have default values
 * This handles existing tasks that don't have the new enhanced columns
 */
export const normalizeEnhancedTodoRow = (row: any): EnhancedTodoRow => {
  // Handle priority field type conversion
  const priority = row.priority as 'low' | 'medium' | 'high';
  const validPriority = ['low', 'medium', 'high'].includes(priority) ? priority : 'medium';

  return {
    // Existing required fields
    id: row.id || '',
    title: row.title || '',
    description: row.description || '',
    priority: validPriority,
    createdAt: row.createdAt || row.created_at || Date.now(),
    updatedAt: row.updatedAt || row.updated_at || Date.now(),
    createdBy: row.createdBy || row.created_by || '',

    // Existing optional fields
    dueDate: row.dueDate || row.due_date || undefined,
    assignedTo: row.assignedTo || row.assigned_to || undefined,
    assignedToName: row.assignedToName || row.assigned_to_name || undefined,
    assignedToPhotoUrl: row.assignedToPhotoUrl || row.assigned_to_photo_url || undefined,
    groupId: row.groupId || row.group_id || undefined,
    columnId: row.columnId || row.column_id || 'column-1',
    column_id: row.column_id || row.columnId || 'column-1',

    // New enhanced columns with defaults (handle both camelCase and snake_case)
    parentid: row.parentid || row.parent_id || row.parentId || undefined,
    subjectid: row.subjectid || row.subject_id || row.subjectId || undefined,
    examid: row.examid || row.exam_id || row.examId || undefined, // Fixed: database uses examid
    tags: Array.isArray(row.tags) ? row.tags : [],
    chaptertags: Array.isArray(row.chaptertags) ? row.chaptertags : (Array.isArray(row.chapter_tags) ? row.chapter_tags : (Array.isArray(row.chapterTags) ? row.chapterTags : [])),
    difficultylevel: row.difficultylevel || row.difficulty_level || row.difficultyLevel || 'medium',
    timeestimate: row.timeestimate || row.time_estimate || row.timeEstimate || undefined,
    actualtimespent: row.actualtimespent || row.actual_time_spent || row.actualTimeSpent || undefined,
    completionpercentage: typeof row.completionpercentage === 'number' ? row.completionpercentage : (typeof row.completion_percentage === 'number' ? row.completion_percentage : (typeof row.completionPercentage === 'number' ? row.completionPercentage : 0)),
    notes: row.notes || undefined,
    viewcount: typeof row.viewcount === 'number' ? row.viewcount : (typeof row.view_count === 'number' ? row.view_count : (typeof row.viewCount === 'number' ? row.viewCount : 0)),
    lastviewed: row.lastviewed || row.last_viewed || row.lastViewed || undefined,
  };
};

/**
 * Converts a database row (with lowercase column names) to client-side item (with camelCase)
 */
export const todoRowToItem = (row: EnhancedTodoRow): EnhancedTodoItem => {
  const normalizedRow = normalizeEnhancedTodoRow(row);
  
  return {
    // Base TodoItem fields
    id: normalizedRow.id,
    title: normalizedRow.title,
    description: normalizedRow.description,
    priority: normalizedRow.priority,
    createdAt: normalizedRow.createdAt,
    updatedAt: normalizedRow.updatedAt,
    dueDate: normalizedRow.dueDate,
    assignedTo: normalizedRow.assignedTo,
    assignedToName: normalizedRow.assignedToName,
    assignedToPhotoURL: normalizedRow.assignedToPhotoUrl, // Convert back to URL for compatibility
    createdBy: normalizedRow.createdBy,
    groupId: normalizedRow.groupId,

    // Enhanced fields (convert to camelCase)
    parentId: normalizedRow.parentid,
    subjectId: normalizedRow.subjectid,
    examId: normalizedRow.examid, // Fixed: database uses examid
    tags: normalizedRow.tags || [],
    chapterTags: normalizedRow.chaptertags || [],
    difficultyLevel: normalizedRow.difficultylevel || 'medium',
    timeEstimate: normalizedRow.timeestimate,
    actualTimeSpent: normalizedRow.actualtimespent,
    completionPercentage: normalizedRow.completionpercentage || 0,
    notes: normalizedRow.notes,
    viewCount: normalizedRow.viewcount || 0,
    lastViewed: normalizedRow.lastviewed,

    // Computed fields (will be calculated separately)
    depth: undefined,
    subtasks: undefined,
    subjectColor: undefined,
    subjectName: undefined,
    examName: undefined,
    examDate: undefined,
    isOverdue: undefined,
    hasSubtasks: undefined,
  };
};

/**
 * Converts a client-side item (with camelCase) to database row format (with lowercase)
 */
export const todoItemToRow = (item: Partial<EnhancedTodoItem>): Partial<EnhancedTodoRow> => {
  const row: Partial<EnhancedTodoRow> = {};

  try {
    // Base fields - only include defined values
    if (item.id !== undefined && item.id !== null) row.id = String(item.id);
    if (item.title !== undefined && item.title !== null) row.title = String(item.title);
    if (item.description !== undefined && item.description !== null) row.description = String(item.description);
    if (item.priority !== undefined && item.priority !== null) row.priority = item.priority;
    if (item.createdAt !== undefined && item.createdAt !== null) row.createdAt = Number(item.createdAt);
    if (item.updatedAt !== undefined && item.updatedAt !== null) row.updatedAt = Number(item.updatedAt);
    if (item.dueDate !== undefined && item.dueDate !== null) row.dueDate = Number(item.dueDate);
    if (item.assignedTo !== undefined && item.assignedTo !== null) row.assignedTo = String(item.assignedTo);
    if (item.assignedToName !== undefined && item.assignedToName !== null) row.assignedToName = String(item.assignedToName);
    if (item.assignedToPhotoURL !== undefined && item.assignedToPhotoURL !== null) row.assignedToPhotoUrl = String(item.assignedToPhotoURL);
    if (item.createdBy !== undefined && item.createdBy !== null) row.createdBy = String(item.createdBy);
    if (item.groupId !== undefined && item.groupId !== null) row.groupId = String(item.groupId);
    if (item.columnId !== undefined && item.columnId !== null) {
      row.columnId = String(item.columnId);
      row.column_id = String(item.columnId); // Set both for compatibility
    }

    // Enhanced fields (convert to lowercase database column names)
    if (item.parentId !== undefined && item.parentId !== null) row.parentid = String(item.parentId);
    if (item.subjectId !== undefined && item.subjectId !== null) row.subjectid = String(item.subjectId);
    if (item.examId !== undefined && item.examId !== null) row.examid = item.examId;
    if (item.tags !== undefined) row.tags = Array.isArray(item.tags) ? item.tags : [];
    if (item.chapterTags !== undefined) row.chaptertags = Array.isArray(item.chapterTags) ? item.chapterTags : [];
    if (item.difficultyLevel !== undefined && item.difficultyLevel !== null) row.difficultylevel = item.difficultyLevel;
    if (item.timeEstimate !== undefined && item.timeEstimate !== null) row.timeestimate = Number(item.timeEstimate);
    if (item.actualTimeSpent !== undefined && item.actualTimeSpent !== null) row.actualtimespent = Number(item.actualTimeSpent);
    if (item.completionPercentage !== undefined && item.completionPercentage !== null) row.completionpercentage = Number(item.completionPercentage);
    if (item.notes !== undefined && item.notes !== null) row.notes = String(item.notes);
    if (item.viewCount !== undefined && item.viewCount !== null) row.viewcount = Number(item.viewCount);
    if (item.lastViewed !== undefined && item.lastViewed !== null) row.lastviewed = Number(item.lastViewed);

    console.log('todoItemToRow conversion:', { input: item, output: row });
    return row;
  } catch (error) {
    console.error('Error in todoItemToRow conversion:', error, item);
    throw new Error(`Failed to convert todo item to row: ${error}`);
  }
};

/**
 * Converts legacy TodoItem to EnhancedTodoItem with default values
 */
export const legacyTodoToEnhanced = (todo: TodoItem): EnhancedTodoItem => {
  return {
    ...todo,
    // Add default values for new enhanced fields
    parentId: undefined,
    subjectId: undefined,
    examId: undefined,
    tags: [],
    chapterTags: [],
    difficultyLevel: 'medium',
    timeEstimate: undefined,
    actualTimeSpent: undefined,
    completionPercentage: 0,
    notes: undefined,
    viewCount: 0,
    lastViewed: undefined,

    // Computed fields
    depth: 0,
    subtasks: [],
    subjectColor: undefined,
    subjectName: undefined,
    examName: undefined,
    examDate: undefined,
    isOverdue: false,
    hasSubtasks: false,
  };
};

/**
 * Checks if a task uses enhanced features
 */
export const isEnhancedTask = (task: EnhancedTodoItem): boolean => {
  return !!(
    task.parentId ||
    task.subjectId ||
    task.examId ||
    task.tags?.length ||
    task.chapterTags?.length ||
    task.difficultyLevel !== 'medium' ||
    task.timeEstimate ||
    task.actualTimeSpent ||
    task.completionPercentage > 0 ||
    task.notes ||
    task.viewCount > 0
  );
};

/**
 * Calculates computed fields for an enhanced todo item
 */
export const calculateComputedFields = (
  task: EnhancedTodoItem,
  allTasks: Record<string, EnhancedTodoItem>,
  subjects: Record<string, any> = {},
  exams: Record<string, any> = {}
): EnhancedTodoItem => {
  const computed = { ...task };

  // Calculate depth from parent chain
  computed.depth = calculateTaskDepth(task.id, allTasks);

  // Find subtasks
  computed.subtasks = Object.values(allTasks)
    .filter(t => t.parentId === task.id)
    .map(t => t.id);

  // Check if has subtasks
  computed.hasSubtasks = computed.subtasks.length > 0;

  // Get subject information
  if (task.subjectId && subjects[task.subjectId]) {
    computed.subjectName = subjects[task.subjectId].name;
    computed.subjectColor = subjects[task.subjectId].color;
  }

  // Get exam information
  if (task.examId && exams[task.examId]) {
    computed.examName = exams[task.examId].name;
    computed.examDate = new Date(exams[task.examId].date).getTime();
  }

  // Check if overdue
  computed.isOverdue = !!(task.dueDate && task.dueDate < Date.now());

  return computed;
};

/**
 * Calculates the depth of a task in the hierarchy
 */
export const calculateTaskDepth = (
  taskId: string,
  allTasks: Record<string, EnhancedTodoItem>,
  visited: Set<string> = new Set()
): number => {
  if (visited.has(taskId)) {
    // Circular reference detected, return 0 to prevent infinite loop
    return 0;
  }

  const task = allTasks[taskId];
  if (!task || !task.parentId) {
    return 0;
  }

  visited.add(taskId);
  return 1 + calculateTaskDepth(task.parentId, allTasks, visited);
};

/**
 * Validates enhanced todo data before saving
 * Handles both full task creation and partial updates
 */
export const validateEnhancedTodo = (todo: Partial<EnhancedTodoItem>, isUpdate: boolean = false): string[] => {
  const errors: string[] = [];

  // Required fields only for creation, not for updates
  if (!isUpdate) {
    if (!todo.title?.trim()) {
      errors.push('Title is required');
    }

    if (!todo.createdBy?.trim()) {
      errors.push('Created by is required');
    }
  } else {
    // For updates, only validate if the field is being updated
    if (todo.title !== undefined && !todo.title?.trim()) {
      errors.push('Title cannot be empty');
    }
  }

  // Validation for enhanced fields (both creation and updates)
  if (todo.completionPercentage !== undefined) {
    if (todo.completionPercentage < 0 || todo.completionPercentage > 100) {
      errors.push('Completion percentage must be between 0 and 100');
    }
  }

  if (todo.timeEstimate !== undefined && todo.timeEstimate < 0) {
    errors.push('Time estimate must be positive');
  }

  if (todo.actualTimeSpent !== undefined && todo.actualTimeSpent < 0) {
    errors.push('Actual time spent must be positive');
  }

  if (todo.difficultyLevel && !['easy', 'medium', 'hard'].includes(todo.difficultyLevel)) {
    errors.push('Difficulty level must be easy, medium, or hard');
  }

  if (todo.priority && !['low', 'medium', 'high'].includes(todo.priority)) {
    errors.push('Priority must be low, medium, or high');
  }

  return errors;
};
