import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  LayoutGrid,
  List,
  Calendar,
  MoreHorizontal,
  CheckSquare,
  Trash2,
  Star,
  BookOpen,
  Target,
  Download,
  Upload,
  Settings,
  BarChart3,
  FileText,
  Activity,
  Users,
} from 'lucide-react';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { SearchAndFilterPanel } from './SearchAndFilterPanel';
import { TaskCreationModal } from './TaskCreationModal';
import { TaskTemplateSystem } from './TaskTemplateSystem';
import { ImportExportSystem } from './ImportExportSystem';
import { ExamLinkageSystem } from './ExamLinkageSystem';
import { TaskAnalyticsDashboard } from './TaskAnalyticsDashboard';
import { TaskAssignmentModal } from './TaskAssignmentModal';
import { TaskSettingsModal } from './TaskSettingsModal';

interface TaskHeaderProps {
  onCreateTask?: () => void;
  onShowAnalytics?: () => void;
  className?: string;
}

export function TaskHeader({ 
  onCreateTask, 
  onShowAnalytics,
  className = '' 
}: TaskHeaderProps) {
  const {
    viewMode,
    setViewMode,
    selectedTasks,
    clearSelection,
    bulkOperation,
    analytics,
    getFilteredTasks,
  } = useEnhancedTodoStore();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isTemplateSystemOpen, setIsTemplateSystemOpen] = useState(false);
  const [isImportExportOpen, setIsImportExportOpen] = useState(false);
  const [importExportMode, setImportExportMode] = useState<'import' | 'export'>('export');
  const [isExamLinkageOpen, setIsExamLinkageOpen] = useState(false);
  const [isAnalyticsDashboardOpen, setIsAnalyticsDashboardOpen] = useState(false);
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);
  const [isTaskSettingsOpen, setIsTaskSettingsOpen] = useState(false);

  const filteredTasks = getFilteredTasks();
  const hasSelectedTasks = selectedTasks.length > 0;

  // View mode options
  const viewModes = [
    {
      id: 'kanban' as const,
      label: 'Kanban',
      icon: LayoutGrid,
      description: 'Card-based board view',
    },
    {
      id: 'table' as const,
      label: 'Table',
      icon: List,
      description: 'Detailed list view',
    },
    {
      id: 'calendar' as const,
      label: 'Calendar',
      icon: Calendar,
      description: 'Timeline view',
    },
  ];

  // Handle bulk operations
  const handleBulkOperation = async (type: string, value?: any) => {
    try {
      await bulkOperation({
        type: type as any,
        taskIds: selectedTasks,
        value,
      });
    } catch (error) {
      console.error('Bulk operation failed:', error);
    }
  };

  // Handle create task
  const handleCreateTask = () => {
    if (onCreateTask) {
      onCreateTask();
    } else {
      setIsCreateModalOpen(true);
    }
  };

  return (
    <>
      <div className={`space-y-6 ${className}`}>
        {/* Main Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Title and Stats */}
            <div>
              <h1 className="font-onest text-2xl font-bold text-gray-900 dark:text-white">
                Task Management
              </h1>
              <div className="flex items-center gap-4 mt-1">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {filteredTasks.length} task{filteredTasks.length !== 1 ? 's' : ''}
                </span>
                {analytics.completionRates.overall > 0 && (
                  <Badge 
                    variant="outline" 
                    className="border-emerald-500/50 text-emerald-400 bg-emerald-500/10"
                  >
                    {Math.round(analytics.completionRates.overall)}% complete
                  </Badge>
                )}
                {analytics.upcomingDeadlines.length > 0 && (
                  <Badge 
                    variant="outline" 
                    className="border-amber-500/50 text-amber-400 bg-amber-500/10"
                  >
                    {analytics.upcomingDeadlines.length} upcoming deadline{analytics.upcomingDeadlines.length !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 md:gap-3">
            {/* Analytics Button - Hidden on mobile */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAnalyticsDashboardOpen(true)}
              className="hidden md:flex border-violet-500/50 text-violet-400 hover:bg-violet-500/20 hover:text-violet-300"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>

            {/* Templates Button - Icon only on mobile */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsTemplateSystemOpen(true)}
              className="border-violet-500/30 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 hover:border-violet-500/50"
            >
              <FileText className="h-4 w-4 md:mr-2" />
              <span className="hidden md:inline">Templates</span>
            </Button>

            {/* Create Task Button */}
            <Button
              onClick={handleCreateTask}
              size="sm"
              className="bg-violet-500 hover:bg-violet-600 text-white shadow-lg shadow-violet-500/25"
            >
              <Plus className="h-4 w-4 md:mr-2" />
              <span className="hidden sm:inline">New Task</span>
            </Button>

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-300 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-600"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800 text-gray-900 dark:text-white"
              >
                {/* Analytics - Mobile only */}
                <DropdownMenuItem
                  className="md:hidden text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                  onClick={() => setIsAnalyticsDashboardOpen(true)}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                  onClick={() => {
                    setImportExportMode('import');
                    setIsImportExportOpen(true);
                  }}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import Tasks
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                  onClick={() => {
                    setImportExportMode('export');
                    setIsImportExportOpen(true);
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Tasks
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-800" />
                <DropdownMenuItem
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                  onClick={() => setIsExamLinkageOpen(true)}
                >
                  <Target className="h-4 w-4 mr-2" />
                  Exam Linkage
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-violet-500/20"
                  onClick={() => setIsTaskSettingsOpen(true)}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Task Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Search and Filter Panel */}
        <SearchAndFilterPanel />

        {/* View Mode Toggle and Bulk Actions */}
        <div className="flex items-center justify-between">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-1 p-1 bg-gray-100/50 dark:bg-gray-800/50 rounded-lg border border-gray-300 dark:border-gray-700">
            {viewModes.map((mode) => (
              <Button
                key={mode.id}
                variant={viewMode === mode.id ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode(mode.id)}
                className={`
                  h-8 px-3 text-xs transition-all duration-200
                  ${viewMode === mode.id
                    ? 'bg-violet-500 text-white shadow-md'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-200/50 dark:hover:bg-gray-700/50'
                  }
                `}
                title={mode.description}
              >
                <mode.icon className="h-3 w-3 mr-1" />
                {mode.label}
              </Button>
            ))}
          </div>

          {/* Bulk Actions */}
          {hasSelectedTasks && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="flex items-center gap-2 p-2 bg-violet-100/80 dark:bg-violet-500/10 border border-violet-300 dark:border-violet-500/30 rounded-lg backdrop-blur-sm"
            >
              <span className="text-sm text-violet-700 dark:text-violet-300 mr-2">
                {selectedTasks.length} selected
              </span>

              {/* Assign Selected */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAssignmentModalOpen(true)}
                className="h-8 px-3 text-xs text-violet-400 hover:text-violet-300 hover:bg-violet-500/20"
              >
                <Users className="h-3 w-3 mr-1" />
                Assign
              </Button>

              {/* Complete Selected */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleBulkOperation('complete')}
                className="h-8 px-3 text-xs text-emerald-400 hover:text-emerald-300 hover:bg-emerald-500/20"
              >
                <CheckSquare className="h-3 w-3 mr-1" />
                Complete
              </Button>

              {/* Priority Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-3 text-xs text-amber-400 hover:text-amber-300 hover:bg-amber-500/20"
                  >
                    <Star className="h-3 w-3 mr-1" />
                    Priority
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#030303]/95 backdrop-blur-md border-gray-800">
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('priority', 'high')}
                    className="text-red-400 hover:text-red-300 hover:bg-red-500/20"
                  >
                    High Priority
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('priority', 'medium')}
                    className="text-amber-400 hover:text-amber-300 hover:bg-amber-500/20"
                  >
                    Medium Priority
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('priority', 'low')}
                    className="text-green-400 hover:text-green-300 hover:bg-green-500/20"
                  >
                    Low Priority
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Subject Assignment */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-3 text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-500/20"
                  >
                    <BookOpen className="h-3 w-3 mr-1" />
                    Subject
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#030303]/95 backdrop-blur-md border-gray-800">
                  {/* Subject options would be populated from store */}
                  <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-violet-500/20">
                    Mathematics
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-violet-500/20">
                    Physics
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-violet-500/20">
                    Chemistry
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Status Change */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-3 text-xs text-gray-400 hover:text-white hover:bg-gray-500/20"
                  >
                    <Target className="h-3 w-3 mr-1" />
                    Status
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-[#030303]/95 backdrop-blur-md border-gray-800">
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('status', 'column-1')}
                    className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                  >
                    Move to Todo
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('status', 'column-2')}
                    className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                  >
                    Move to In Progress
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleBulkOperation('status', 'column-3')}
                    className="text-gray-300 hover:text-white hover:bg-violet-500/20"
                  >
                    Move to Done
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Delete Selected */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleBulkOperation('delete')}
                className="h-8 px-3 text-xs text-red-400 hover:text-red-300 hover:bg-red-500/20"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>

              {/* Clear Selection */}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSelection}
                className="h-8 px-2 text-xs text-gray-400 hover:text-white hover:bg-gray-500/20"
              >
                Clear
              </Button>
            </motion.div>
          )}
        </div>
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      {/* Task Template System */}
      <TaskTemplateSystem
        isOpen={isTemplateSystemOpen}
        onClose={() => setIsTemplateSystemOpen(false)}
      />

      {/* Import/Export System */}
      <ImportExportSystem
        isOpen={isImportExportOpen}
        onClose={() => setIsImportExportOpen(false)}
        mode={importExportMode}
      />

      {/* Exam Linkage System */}
      <ExamLinkageSystem
        isOpen={isExamLinkageOpen}
        onClose={() => setIsExamLinkageOpen(false)}
      />

      {/* Task Analytics Dashboard */}
      <TaskAnalyticsDashboard
        isOpen={isAnalyticsDashboardOpen}
        onClose={() => setIsAnalyticsDashboardOpen(false)}
      />

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignmentModalOpen}
        onClose={() => setIsAssignmentModalOpen(false)}
        taskIds={selectedTasks}
      />

      {/* Task Settings Modal */}
      <TaskSettingsModal
        isOpen={isTaskSettingsOpen}
        onClose={() => setIsTaskSettingsOpen(false)}
      />


    </>
  );
}
