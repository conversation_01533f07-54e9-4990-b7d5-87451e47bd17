import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import { TaskCreationModal } from './TaskCreationModal';
import { format, formatDistanceToNow } from 'date-fns';
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Download,
  Settings,
  Plus,
  Eye,
  Calendar,
  Clock,
  Target,
  BookOpen,
  Tag,
  CheckCircle2,
  Circle,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Column {
  key: keyof EnhancedTodoItem | 'actions' | 'select';
  label: string;
  sortable: boolean;
  visible: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface SortConfig {
  key: keyof EnhancedTodoItem | null;
  direction: 'asc' | 'desc';
}

const defaultColumns: Column[] = [
  { key: 'select', label: '', sortable: false, visible: true, width: '50px', align: 'center' },
  { key: 'title', label: 'Task', sortable: true, visible: true, width: '300px' },
  { key: 'priority', label: 'Priority', sortable: true, visible: true, width: '100px', align: 'center' },
  { key: 'dueDate', label: 'Due Date', sortable: true, visible: true, width: '120px' },
  { key: 'subjectId', label: 'Subject', sortable: true, visible: true, width: '120px' },
  { key: 'difficultyLevel', label: 'Difficulty', sortable: true, visible: true, width: '100px', align: 'center' },
  { key: 'completionPercentage', label: 'Progress', sortable: true, visible: true, width: '120px' },
  { key: 'timeEstimate', label: 'Estimate', sortable: true, visible: true, width: '100px', align: 'center' },
  { key: 'createdAt', label: 'Created', sortable: true, visible: true, width: '120px' },
  { key: 'actions', label: 'Actions', sortable: false, visible: true, width: '80px', align: 'center' },
];

export function EnhancedTableView() {
  const {
    getFilteredTasks,
    updateTask,
    deleteTask,
    selectedTasks,
    selectTask,
    selectAllTasks,
    clearSelection,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<EnhancedTodoItem | null>(null);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'createdAt', direction: 'desc' });
  const [filterConfig] = useState({
    priority: '',
    status: '',
    subject: '',
    assignee: '',
  });

  const [columns, setColumns] = useState<Column[]>(defaultColumns);
  const [editingCell, setEditingCell] = useState<{ taskId: string; field: keyof EnhancedTodoItem } | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  const allTasks = getFilteredTasks();

  // Apply additional filters and sorting
  const sortedTasks = useMemo(() => {
    // First apply filters
    const filteredTasks = allTasks.filter(task => {
      if (filterConfig.priority && task.priority !== filterConfig.priority) return false;
      if (filterConfig.status) {
        if (filterConfig.status === 'completed' && task.completionPercentage < 100) return false;
        if (filterConfig.status === 'pending' && task.completionPercentage >= 100) return false;
      }
      if (filterConfig.subject && task.subjectId !== filterConfig.subject) return false;
      if (filterConfig.assignee && task.assignedTo !== filterConfig.assignee) return false;
      return true;
    });

    // Then apply sorting
    if (!sortConfig.key) return filteredTasks;

    return [...filteredTasks].sort((a, b) => {
      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;

      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortConfig.direction === 'desc' ? -comparison : comparison;
    });
  }, [allTasks, filterConfig, sortConfig]);



  // Handle sorting
  const handleSort = useCallback((key: keyof EnhancedTodoItem) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  // Handle column visibility toggle
  const toggleColumnVisibility = useCallback((columnKey: string) => {
    setColumns(prev => prev.map(col => 
      col.key === columnKey ? { ...col, visible: !col.visible } : col
    ));
  }, []);

  // Handle inline editing
  const startEditing = useCallback((taskId: string, field: keyof EnhancedTodoItem, currentValue: any) => {
    setEditingCell({ taskId, field });
    setEditValue(String(currentValue || ''));
  }, []);

  const saveEdit = useCallback(async () => {
    if (!editingCell) return;

    try {
      let value: any = editValue;
      
      // Convert value based on field type
      if (editingCell.field === 'timeEstimate' || editingCell.field === 'completionPercentage') {
        value = parseInt(editValue) || 0;
      } else if (editingCell.field === 'dueDate') {
        value = new Date(editValue).getTime();
      }

      await updateTask(editingCell.taskId, { [editingCell.field]: value });
      setEditingCell(null);
      setEditValue('');
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  }, [editingCell, editValue, updateTask]);

  const cancelEdit = useCallback(() => {
    setEditingCell(null);
    setEditValue('');
  }, []);

  // Handle bulk selection
  const isAllSelected = selectedTasks.length === sortedTasks.length && sortedTasks.length > 0;
  const isIndeterminate = selectedTasks.length > 0 && selectedTasks.length < sortedTasks.length;

  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection();
    } else {
      selectAllTasks(sortedTasks.map(task => task.id));
    }
  }, [isAllSelected, clearSelection, selectAllTasks, sortedTasks]);

  // Export functionality
  const exportToCSV = useCallback(() => {
    const visibleColumns = columns.filter(col => col.visible && col.key !== 'select' && col.key !== 'actions');
    const headers = visibleColumns.map(col => col.label);
    
    const csvData = [
      headers.join(','),
      ...sortedTasks.map(task => 
        visibleColumns.map(col => {
          const value = task[col.key as keyof EnhancedTodoItem];
          if (col.key === 'dueDate' && value) {
            return format(new Date(value as number), 'yyyy-MM-dd');
          }
          if (col.key === 'subjectId' && value) {
            return subjects[value as string]?.name || value;
          }
          return String(value || '');
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tasks-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }, [columns, sortedTasks, subjects]);

  // Render cell content
  const renderCell = useCallback((task: EnhancedTodoItem, column: Column) => {
    const isEditing = editingCell?.taskId === task.id && editingCell?.field === column.key;
    
    if (isEditing) {
      return (
        <Input
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={saveEdit}
          onKeyDown={(e) => {
            if (e.key === 'Enter') saveEdit();
            if (e.key === 'Escape') cancelEdit();
          }}
          className="h-8 text-xs"
          autoFocus
        />
      );
    }

    switch (column.key) {
      case 'select':
        return (
          <Checkbox
            checked={selectedTasks.includes(task.id)}
            onCheckedChange={() => selectTask(task.id)}
          />
        );

      case 'title':
        return (
          <div className="space-y-1">
            <div 
              className="font-medium cursor-pointer hover:text-primary"
              onClick={() => startEditing(task.id, 'title', task.title)}
            >
              {task.title}
            </div>
            {task.description && (
              <div className="text-xs text-muted-foreground line-clamp-1">
                {task.description}
              </div>
            )}
            {task.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {task.tags.slice(0, 2).map(tag => (
                  <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                    {tag}
                  </Badge>
                ))}
                {task.tags.length > 2 && (
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    +{task.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        );

      case 'priority':
        return (
          <Badge 
            variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {task.priority}
          </Badge>
        );

      case 'dueDate':
        if (!task.dueDate) return <span className="text-muted-foreground">-</span>;
        const dueDate = new Date(task.dueDate);
        const isOverdue = dueDate < new Date();
        return (
          <div className={cn("text-xs", isOverdue && "text-destructive")}>
            <div>{format(dueDate, 'MMM dd')}</div>
            <div className="text-muted-foreground">
              {formatDistanceToNow(dueDate, { addSuffix: true })}
            </div>
          </div>
        );

      case 'subjectId':
        if (!task.subjectId) return <span className="text-muted-foreground">-</span>;
        const subject = subjects[task.subjectId];
        return subject ? (
          <Badge style={{ backgroundColor: subject.color + '20', color: subject.color }}>
            {subject.name}
          </Badge>
        ) : <span className="text-muted-foreground">Unknown</span>;

      case 'difficultyLevel':
        const difficultyColors = {
          easy: 'text-green-600',
          medium: 'text-yellow-600',
          hard: 'text-red-600',
        };
        return (
          <Badge variant="outline" className={cn("capitalize", difficultyColors[task.difficultyLevel])}>
            {task.difficultyLevel}
          </Badge>
        );

      case 'completionPercentage':
        return (
          <div className="space-y-1">
            <Progress value={task.completionPercentage} className="h-2" />
            <div className="text-xs text-center">{task.completionPercentage}%</div>
          </div>
        );

      case 'timeEstimate':
        if (!task.timeEstimate) return <span className="text-muted-foreground">-</span>;
        return (
          <div className="text-xs text-center">
            {Math.floor(task.timeEstimate / 60)}h {task.timeEstimate % 60}m
          </div>
        );

      case 'createdAt':
        return (
          <div className="text-xs text-muted-foreground">
            {format(new Date(task.createdAt), 'MMM dd, yyyy')}
          </div>
        );

      case 'actions':
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => startEditing(task.id, 'title', task.title)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => deleteTask(task.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );

      default:
        const value = task[column.key as keyof EnhancedTodoItem];
        return <span>{String(value || '-')}</span>;
    }
  }, [editingCell, editValue, selectedTasks, subjects, startEditing, saveEdit, cancelEdit, selectTask, deleteTask]);

  return (
    <div className="space-y-4">
      {/* Table Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {selectedTasks.length > 0 ? `${selectedTasks.length} selected` : `${sortedTasks.length} tasks`}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setIsCreateModalOpen(true)}
            className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </Button>

          <Button variant="outline" size="sm" onClick={exportToCSV}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {columns.filter(col => col.key !== 'select' && col.key !== 'actions').map(column => (
                <DropdownMenuCheckboxItem
                  key={column.key}
                  checked={column.visible}
                  onCheckedChange={() => toggleColumnVisibility(column.key)}
                >
                  {column.label}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-lg border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.filter(col => col.visible).map(column => (
                  <TableHead
                    key={column.key}
                    className={cn(
                      "select-none whitespace-nowrap",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right",
                      column.sortable && "cursor-pointer hover:bg-muted/50"
                    )}
                    style={{ width: column.width, minWidth: column.key === 'title' ? '200px' : 'auto' }}
                    onClick={() => column.sortable && handleSort(column.key as keyof EnhancedTodoItem)}
                  >
                  <div className="flex items-center gap-2">
                    {column.key === 'select' ? (
                      <Checkbox
                        checked={isAllSelected}
                        ref={(el) => {
                          if (el) {
                            const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                            if (checkbox) checkbox.indeterminate = isIndeterminate;
                          }
                        }}
                        onCheckedChange={handleSelectAll}
                      />
                    ) : (
                      <>
                        {column.label}
                        {column.sortable && (
                          <div className="flex flex-col">
                            {sortConfig.key === column.key ? (
                              sortConfig.direction === 'asc' ? (
                                <ArrowUp className="h-3 w-3" />
                              ) : (
                                <ArrowDown className="h-3 w-3" />
                              )
                            ) : (
                              <ArrowUpDown className="h-3 w-3 opacity-50" />
                            )}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            <AnimatePresence>
              {sortedTasks.map((task, index) => (
                <motion.tr
                  key={task.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.02 }}
                  className={cn(
                    "border-b transition-colors hover:bg-muted/50",
                    selectedTasks.includes(task.id) && "bg-muted/30"
                  )}
                >
                  {columns.filter(col => col.visible).map(column => (
                    <TableCell 
                      key={column.key}
                      className={cn(
                        column.align === 'center' && "text-center",
                        column.align === 'right' && "text-right"
                      )}
                    >
                      {renderCell(task, column)}
                    </TableCell>
                  ))}
                </motion.tr>
              ))}
            </AnimatePresence>
          </TableBody>
          </Table>
        </div>

        {sortedTasks.length === 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No tasks found</p>
            <p className="text-sm">Create your first task to get started</p>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateModalOpen(true)}
              className="mt-4 border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create First Task
            </Button>
          </div>
        )}
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setEditingTask(null);
        }}
        editingTask={editingTask}
      />
    </div>
  );
}
